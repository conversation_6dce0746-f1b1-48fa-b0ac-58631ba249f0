import argparse
import sys
from pathlib import Path
from typing import List, Tuple, Optional, Dict


def _safe_import_python_docx():
	try:
		import docx  # type: ignore
		from docx.table import _Cell, Table  # type: ignore
		from docx.document import Document as _Document  # type: ignore
		return docx, _Cell, Table, _Document
	except Exception as e:
		print(
			"未找到或无法导入 python-docx，请先安装: pip install python-docx",
			file=sys.stderr,
		)
		raise


def get_cell_text(cell) -> str:
	# Join all paragraphs' text and strip whitespace
	try:
		text = "".join(p.text or "" for p in cell.paragraphs)
	except Exception:
		# Fallback to cell.text if paragraphs API differs
		text = getattr(cell, "text", "")
	return (text or "").strip()


def is_placeholder_text(text: str) -> bool:
	"""Return True if text already looks like a placeholder, e.g. "{字段}".

	We treat such cells as non-sources to avoid cascading placeholders to the right,
	which can lead to outputs like "{{所在团支部}}".
	"""
	if not text:
		return False
	s = text.strip()
	# simple and robust check: starts with one '{' and ends with one '}',
	# and has at least one non-brace character inside.
	return len(s) >= 2 and s[0] == "{" and s[-1] == "}" and any(ch not in "{}\t\n\r " for ch in s[1:-1])


def get_grid_span(cell) -> int:
	# Horizontal merge width (gridSpan)
	try:
		tcPr = cell._tc.tcPr  # noqa: SLF001
		if tcPr is not None and tcPr.gridSpan is not None and tcPr.gridSpan.val:
			return int(tcPr.gridSpan.val)
	except Exception:
		pass
	return 1


def get_vmerge_state(cell) -> str:
	"""Return vertical merge state for a cell: 'none' | 'restart' | 'continue'."""
	try:
		tcPr = cell._tc.tcPr  # noqa: SLF001
		if tcPr is None or tcPr.vMerge is None:
			return "none"
		val = tcPr.vMerge.val
		# In DOCX, vMerge without a val often means 'continue'
		if val is None:
			return "continue"
		if str(val).lower() == "restart":
			return "restart"
		# Some producers may explicitly set 'continue'
		if str(val).lower() == "continue":
			return "continue"
	except Exception:
		pass
	return "none"


def calc_vertical_span(table, start_row: int, first_col_idx: int) -> int:
	"""Compute how many rows a cell spans vertically by scanning downward.

	We consider the cell that begins at (start_row, first_col_idx). Count 1 for the start
	row, then add rows while the cell at the same column index is in 'continue' state.
	"""
	rows = table.rows
	span = 1
	# Scan downward until vMerge stops
	for r in range(start_row + 1, len(rows)):
		c = rows[r].cells[first_col_idx]
		state = get_vmerge_state(c)
		if state == "continue":
			span += 1
		else:
			break
	return span


def build_row_unique_cells(row) -> List[Tuple[int, object]]:
	"""Return a list of (first_col_idx, cell) for unique cells in a row.

	python-docx returns one cell per grid column; horizontally merged cells appear as
	repeated references to the same _Cell object. We only keep the first occurrence
	for each unique cell object in left-to-right order. Additionally, we skip cells
	that are vertical-merge continuations (they don't start at this row).
	"""
	uniq = []
	seen_ids = set()
	cells = row.cells
	for idx, cell in enumerate(cells):
		cid = id(cell)
		if cid in seen_ids:
			continue
		seen_ids.add(cid)
		if get_vmerge_state(cell) == "continue":
			# This row position is covered by a vertical merge started above; skip.
			continue
		uniq.append((idx, cell))
	return uniq


def fill_placeholders_for_table(table) -> int:
	"""Two-pass processing of one table.

	Pass 1: For each non-empty source cell, find the nearest right empty cell
	with the same vertical span and record (target_cell, "{text}") without modifying.

	Pass 2: Apply all recorded updates. Returns the number of cells successfully modified.
	"""
	# Pass 1: collect updates without mutating the table
	planned_updates = []  # list[(cell, text)]
	reserved_targets = set()  # prevent multiple sources choosing the same target

	for r_idx, row in enumerate(table.rows):
		uniq_cells = build_row_unique_cells(row)
		if not uniq_cells:
			continue
		for i, (col_idx, cell) in enumerate(uniq_cells):
			src_text = get_cell_text(cell)
			# Skip empty cells and cells that already contain a placeholder
			# to prevent cascading (e.g., {所在团支部} becoming {{所在团支部}} to the right).
			if not src_text or is_placeholder_text(src_text):
				continue
			vspan_left = calc_vertical_span(table, r_idx, col_idx)
			# Find nearest right candidate meeting conditions from the original (unmodified) state
			for j in range(i + 1, len(uniq_cells)):
				right_col_idx, right_cell = uniq_cells[j]
				# Plan 2: if we hit a non-empty cell, stop scanning further to the right.
				# This prevents jumping over label/hint cells like "年月日" to the far-right empty cell.
				if get_cell_text(right_cell):
					break

				# Match vertical span only after confirming emptiness
				vspan_right = calc_vertical_span(table, r_idx, right_col_idx)
				if vspan_right != vspan_left:
					continue
				# Ensure this cell isn't already reserved by an earlier source
				rc_id = id(right_cell)
				if rc_id in reserved_targets:
					continue
				planned_updates.append((right_cell, f"{{{src_text}}}"))
				reserved_targets.add(rc_id)
				break  # Only the nearest right cell

	# Pass 2: apply updates
	modified = 0
	for target_cell, content in planned_updates:
		try:
			target_cell.text = content
			modified += 1
		except Exception:
			# Attempt a safe write by clearing runs in existing paragraphs
			try:
				for p in list(target_cell.paragraphs):
					for run in list(p.runs):
						p._p.remove(run._r)  # noqa: SLF001
				target_cell.paragraphs[0].add_run(content)
				modified += 1
			except Exception:
				# Give up on this cell
				pass
	return modified


def process_docx(input_path: Path, output_path: Optional[Path] = None) -> Tuple[Path, int]:
	docx, _Cell, Table, _Document = _safe_import_python_docx()
	Document = docx.Document
	doc = Document(str(input_path))
	total_modified = 0
	for tbl in doc.tables:
		total_modified += fill_placeholders_for_table(tbl)
	if output_path is None:
		output_path = input_path.with_name(f"{input_path.stem}_templated{input_path.suffix}")
	doc.save(str(output_path))
	return output_path, total_modified



if __name__ == "__main__":
    in_path = "模板.docx"
    out_path = "模板_templated.docx"
    process_docx(in_path, out_path)
